/**
 * 订阅管理页面
 */

import {
  CrownOutlined,
  HistoryOutlined,
  ReloadOutlined,
  StopOutlined,
  UpOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  Descriptions,
  Modal,
  message,
  Progress,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import { SubscriptionService } from '@/services';
import type { SubscriptionResponse } from '@/types/api';
import { SubscriptionStatus } from '@/types/api';

const { Title, Text } = Typography;

const SubscriptionManagePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentSubscription, setCurrentSubscription] =
    useState<SubscriptionResponse | null>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<
    SubscriptionResponse[]
  >([]);
  const [usageInfo, setUsageInfo] = useState<any>(null);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);

      // 先获取用户订阅列表，避免重复调用
      const subscriptions = await SubscriptionService.getUserSubscriptions();

      // 基于已获取的订阅列表计算其他数据
      const current =
        subscriptions.find((sub) => sub.status === SubscriptionStatus.ACTIVE) ||
        null;
      const history = subscriptions.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );

      // 计算使用情况
      let usage = {
        currentUsage: 0,
        maxUsage: 0,
        usagePercentage: 0,
        remainingDays: 0,
      };

      if (current) {
        const endDate = new Date(current.endDate);
        const now = new Date();
        const remainingDays = Math.max(
          0,
          Math.ceil(
            (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
          ),
        );

        usage = {
          currentUsage: 0, // 实际使用量
          maxUsage: current.maxSize,
          usagePercentage: current.maxSize > 0 ? 0 : 0, // 暂时设为0，等后端提供实际数据
          remainingDays,
        };
      }

      setCurrentSubscription(current);
      setSubscriptionHistory(history);
      setUsageInfo(usage);
    } catch (error) {
      console.error('获取订阅信息失败:', error);
      message.error('获取订阅信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = (subscription: SubscriptionResponse) => {
    Modal.confirm({
      title: '确认取消订阅',
      content:
        '取消订阅后，您将在当前计费周期结束后失去高级功能。确定要取消吗？',
      okText: '确认取消',
      cancelText: '保留订阅',
      okType: 'danger',
      onOk: async () => {
        try {
          await SubscriptionService.cancelSubscription(subscription.id);
          message.success('订阅已取消');
          fetchSubscriptionData();
        } catch (error) {
          console.error('取消订阅失败:', error);
        }
      },
    });
  };

  const handleRenewSubscription = (subscription: SubscriptionResponse) => {
    Modal.confirm({
      title: '确认续费订阅',
      content: '是否要续费当前订阅1个月？',
      okText: '确认续费',
      cancelText: '取消',
      onOk: async () => {
        try {
          await SubscriptionService.renewSubscription(subscription.id, 1);
          message.success('续费成功');
          fetchSubscriptionData();
        } catch (error) {
          console.error('续费失败:', error);
        }
      },
    });
  };

  const getStatusTag = (status: SubscriptionStatus) => {
    const statusConfig = {
      ACTIVE: { color: 'green', text: '活跃' },
      EXPIRED: { color: 'red', text: '已过期' },
      CANCELLED: { color: 'orange', text: '已取消' },
    };

    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const historyColumns: ColumnsType<SubscriptionResponse> = [
    {
      title: '套餐名称',
      dataIndex: 'planName',
      key: 'planName',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price) => `¥${price}/月`,
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
  ];

  if (loading) {
    return (
      <PageContainer>
        <Card loading={loading} />
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="订阅管理"
      extra={
        <Button
          icon={<HistoryOutlined />}
          onClick={() => setHistoryModalVisible(true)}
        >
          订阅历史
        </Button>
      }
    >
      {currentSubscription ? (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 当前订阅信息 */}
          <Card
            title={
              <>
                <CrownOutlined /> 当前订阅
              </>
            }
          >
            <Descriptions column={2}>
              <Descriptions.Item label="套餐名称">
                {currentSubscription.planName}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(currentSubscription.status)}
              </Descriptions.Item>
              <Descriptions.Item label="月费">
                ¥{currentSubscription.price}
              </Descriptions.Item>
              <Descriptions.Item label="存储上限">
                {currentSubscription.maxSize}GB
              </Descriptions.Item>
              <Descriptions.Item label="开始时间">
                {new Date(currentSubscription.startDate).toLocaleDateString()}
              </Descriptions.Item>
              <Descriptions.Item label="到期时间">
                {new Date(currentSubscription.endDate).toLocaleDateString()}
              </Descriptions.Item>
              <Descriptions.Item label="套餐描述" span={2}>
                {currentSubscription.planDescription}
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={() => handleRenewSubscription(currentSubscription)}
                  disabled={
                    currentSubscription.status !== SubscriptionStatus.ACTIVE
                  }
                >
                  续费订阅
                </Button>

                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={() => handleCancelSubscription(currentSubscription)}
                  disabled={
                    currentSubscription.status !== SubscriptionStatus.ACTIVE
                  }
                >
                  取消订阅
                </Button>
              </Space>
            </div>
          </Card>

          {/* 使用情况 */}
          {usageInfo && (
            <Card title="使用情况">
              <div style={{ marginBottom: 16 }}>
                <Text strong>存储使用量</Text>
                <Progress
                  percent={usageInfo.usagePercentage}
                  format={() =>
                    `${usageInfo.currentUsage}GB / ${usageInfo.maxUsage}GB`
                  }
                  status={
                    usageInfo.usagePercentage > 80 ? 'exception' : 'normal'
                  }
                  style={{ marginTop: 8 }}
                />
              </div>

              <div>
                <Text strong>剩余天数：</Text>
                <Text
                  style={{ marginLeft: 8, fontSize: 18, fontWeight: 'bold' }}
                >
                  {usageInfo.remainingDays} 天
                </Text>
              </div>

              {usageInfo.usagePercentage > 80 && (
                <Alert
                  message="存储空间不足"
                  description="您的存储空间使用量已超过80%，建议升级套餐或清理数据。"
                  type="warning"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              )}

              {usageInfo.remainingDays <= 7 && usageInfo.remainingDays > 0 && (
                <Alert
                  message="订阅即将到期"
                  description={`您的订阅将在${usageInfo.remainingDays}天后到期，请及时续费以免影响使用。`}
                  type="warning"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              )}
            </Card>
          )}
        </Space>
      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <CrownOutlined
              style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }}
            />
            <Title level={3} type="secondary">
              您还没有活跃的订阅
            </Title>
            <Text
              type="secondary"
              style={{ marginBottom: 24, display: 'block' }}
            >
              选择一个适合您的订阅套餐，享受更多高级功能
            </Text>

          </div>
        </Card>
      )}

      {/* 订阅历史模态框 */}
      <Modal
        title="订阅历史"
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          columns={historyColumns}
          dataSource={subscriptionHistory}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Modal>
    </PageContainer>
  );
};

export default SubscriptionManagePage;
