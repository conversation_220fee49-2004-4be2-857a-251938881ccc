import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import {
  ErrorBoundary,
  Footer,
  MessageProvider,
} from '@/components';
import UserFloatButton from '@/components/FloatButton';
import { AuthService, UserService, TeamService } from '@/services';
import type { UserProfileResponse, TeamDetailResponse } from '@/types/api';
import { hasTeamInCurrentToken } from '@/utils/tokenUtils';



/**
 * 全局初始化数据配置函数
 *
 * 这是UmiJS应用的核心初始化函数，负责在应用启动时设置全局状态。
 * 主要用于Layout组件的用户信息和权限初始化，是整个身份验证系统的入口点。
 *
 * 主要功能：
 * 1. 检查用户登录状态并获取用户信息
 * 2. 根据Token状态获取团队信息
 * 3. 提供数据获取函数供组件使用
 * 4. 处理初始化过程中的各种异常情况
 * 5. 为路由守卫提供必要的状态信息
 *
 * 初始化流程：
 * 1. 定义用户信息和团队信息的获取函数
 * 2. 检查当前路径是否需要身份验证
 * 3. 验证用户登录状态
 * 4. 获取用户基本信息
 * 5. 根据Token中的团队信息获取团队详情
 * 6. 返回初始化状态供全局使用
 *
 * 状态管理：
 * - currentUser：当前登录用户的详细信息
 * - currentTeam：当前选择团队的详细信息
 * - fetchUserInfo：重新获取用户信息的函数
 * - fetchTeamInfo：重新获取团队信息的函数
 *
 * 错误处理策略：
 * - Token过期：自动清除本地Token，返回未登录状态
 * - 网络错误：记录日志但不阻止应用启动
 * - 权限错误：返回安全的默认状态
 *
 * 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
 */
export async function getInitialState(): Promise<{
  currentUser?: UserProfileResponse;
  currentTeam?: TeamDetailResponse;
  loading?: boolean;
  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;
  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;
}> {
  /**
   * 获取用户信息的函数
   *
   * 从后端API获取当前登录用户的详细信息，包括用户ID、邮箱、姓名等。
   * 这个函数会被存储在全局状态中，供组件在需要时调用。
   *
   * 错误处理：
   * - Token过期或无效：自动清除本地Token
   * - 网络错误：返回undefined，让调用方处理
   * - 权限错误：记录日志并返回undefined
   *
   * @returns Promise<UserProfileResponse | undefined> 用户信息或undefined
   */
  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {
    try {
      // 调用用户服务获取当前用户的详细信息
      return await UserService.getUserProfile();
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，可能是Token过期或无效
      // 清除本地Token，让用户重新登录
      AuthService.clearToken();
      return undefined;
    }
  };

  /**
   * 获取团队信息的函数
   *
   * 从后端API获取当前选择团队的详细信息，包括团队名称、成员数量等。
   * 只有在Token包含团队信息时才会尝试获取，避免不必要的API调用。
   *
   * 团队状态检查：
   * - 检查Token中是否包含teamId
   * - 只有团队Token才会调用团队API
   * - 用户Token不会触发团队信息获取
   *
   * @returns Promise<TeamDetailResponse | undefined> 团队信息或undefined
   */
  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {
    try {
      // 检查当前Token是否包含团队信息
      // 只有在用户已选择团队的情况下才尝试获取团队详情
      if (!hasTeamInCurrentToken()) {
        return undefined;
      }

      // 调用团队服务获取当前团队的详细信息
      return await TeamService.getCurrentTeamDetail();
    } catch (error) {
      console.error('获取团队信息失败:', error);
      // 团队信息获取失败不影响用户基本功能
      // 返回undefined，让组件显示无团队状态
      return undefined;
    }
  };



  /**
   * 路径检查和初始化逻辑
   *
   * 根据当前访问的路径决定是否需要进行身份验证初始化。
   * 对于公开页面（如登录、注册），跳过身份验证检查。
   * 对于需要身份验证的页面，执行完整的初始化流程。
   */
  const { location } = history;

  // 检查当前路径是否为公开页面（不需要身份验证）
  if (!['/user/login', '/user/register'].includes(location.pathname)) {
    try {
      /**
       * 身份验证检查
       *
       * 首先检查用户是否已登录（本地是否有有效Token）。
       * 如果未登录，返回基础状态，让路由守卫处理跳转。
       */
      if (!AuthService.isLoggedIn()) {
        // 用户未登录，返回基础状态
        // 路由守卫会检测到currentUser为空并跳转到登录页
        return {
          fetchUserInfo,
          fetchTeamInfo,
        };
      }

      /**
       * 用户信息获取
       *
       * 用户已登录，尝试获取用户的详细信息。
       * 这一步会验证Token的有效性，如果Token过期会自动清除。
       */
      const currentUser = await fetchUserInfo();
      if (!currentUser) {
        // 用户信息获取失败，可能是Token过期
        // 返回基础状态，让路由守卫处理重新登录
        return {
          fetchUserInfo,
          fetchTeamInfo,
        };
      }

      /**
       * 团队信息获取
       *
       * 用户信息获取成功后，尝试获取团队信息。
       * 只有在Token包含团队信息时才会执行，避免不必要的API调用。
       * 团队信息获取失败不影响用户基本功能的使用。
       */
      const currentTeam = await fetchTeamInfo();

      /**
       * 返回完整的初始化状态
       *
       * 包含用户信息、团队信息（如果有）和数据获取函数。
       * 这些信息会被存储在全局状态中，供整个应用使用。
       */
      return {
        fetchUserInfo,
        fetchTeamInfo,
        currentUser,
        currentTeam,
      };
    } catch (error) {
      /**
       * 初始化异常处理
       *
       * 捕获初始化过程中的所有异常，确保应用能够正常启动。
       * 即使初始化失败，也要返回基础状态，让用户能够重新登录。
       */
      console.error('初始化失败:', error);
      return {
        fetchUserInfo,
        fetchTeamInfo,
      };
    }
  }

  /**
   * 公开页面的默认状态
   *
   * 对于登录、注册等公开页面，不需要进行身份验证检查。
   * 只返回数据获取函数，供页面在需要时使用。
   */
  return {
    fetchUserInfo,
    fetchTeamInfo,
  };
}

/**
 * ProLayout 布局配置
 *
 * 这是UmiJS应用的布局配置函数，定义了整个应用的UI布局和行为。
 * 主要负责页面布局、路由守卫、用户状态显示等功能。
 *
 * 主要功能：
 * 1. 页面布局和样式配置
 * 2. 路由切换时的身份验证检查
 * 3. 用户信息的显示（如水印）
 * 4. 全局组件的渲染（如错误边界、消息提供者）
 * 5. 自定义页面元素的配置
 *
 * 路由守卫机制：
 * - 在每次页面切换时检查用户登录状态
 * - 未登录用户自动跳转到登录页面
 * - 公开页面（登录、注册）允许匿名访问
 *
 * ProLayout 支持的API: https://procomponents.ant.design/components/layout
 */
export const layout: RunTimeLayoutConfig = ({ initialState }: any) => {
  return {
    /**
     * 水印配置
     *
     * 在页面上显示用户名水印，用于标识当前登录用户。
     * 只有在用户已登录时才显示水印。
     */
    waterMarkProps: {
      content: initialState?.currentUser?.name,
    },

    /**
     * 底部版权信息
     *
     * 渲染应用底部的版权信息和相关链接。
     */
    footerRender: () => <Footer />,

    /**
     * 右侧内容渲染
     *
     * 移除默认的右侧内容（如用户头像、设置等），
     * 使用自定义的FloatButton组件替代。
     */
    rightContentRender: false,

    /**
     * 页面切换时的路由守卫
     *
     * 这是应用级别的路由守卫，在每次页面切换时执行身份验证检查。
     * 确保只有已登录的用户才能访问需要身份验证的页面。
     *
     * 守卫逻辑：
     * 1. 检查全局状态中是否有用户信息
     * 2. 检查当前路径是否为公开页面
     * 3. 未登录用户访问受保护页面时自动跳转到登录页
     *
     * 公开页面列表：
     * - /user/login：用户登录页面
     * - /user/register：用户注册页面
     */
    onPageChange: () => {
      const { location } = history;

      // 检查用户是否已登录且当前页面是否需要身份验证
      if (
        !initialState?.currentUser &&
        !['/user/login', '/user/register'].includes(location.pathname)
      ) {
        // 用户未登录且访问受保护页面，跳转到登录页
        history.push('/user/login');
      }
    },
    /**
     * 背景图片配置
     *
     * 自定义布局区域的背景装饰图片，用于美化页面视觉效果。
     * 这些图片会作为背景元素显示在页面的不同位置。
     */
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],

    /**
     * 自定义链接配置
     *
     * 页面底部或侧边栏的自定义链接列表。
     * 当前为空数组，表示不显示额外的链接。
     */
    links: [],

    /**
     * 菜单头部渲染
     *
     * 自定义侧边栏菜单的头部内容。
     * 设置为undefined表示使用默认的菜单头部。
     */
    menuHeaderRender: undefined,

    /**
     * 自定义403权限不足页面
     *
     * 当用户访问没有权限的页面时显示的内容。
     * 当前被注释掉，使用系统默认的403页面。
     */
    // unAccessible: <div>unAccessible</div>,

    /**
     * 子组件渲染器
     *
     * 这是整个应用的根组件包装器，为所有页面提供全局功能。
     * 包含错误边界、消息提供者和全局浮动按钮等功能。
     *
     * 全局组件说明：
     * - ErrorBoundary：捕获和处理React组件错误
     * - MessageProvider：提供全局消息通知功能
     * - UserFloatButton：用户操作的浮动按钮（登录后显示）
     *
     * 这些组件确保了应用的稳定性和用户体验的一致性。
     */
    childrenRender: (children: any) => {
      return (
        <ErrorBoundary>
          <MessageProvider>
            {children}
            {/* 全局 FloatButton - 在用户登录后显示 */}
            <UserFloatButton />
          </MessageProvider>
        </ErrorBoundary>
      );
    },

    /**
     * 设置配置
     *
     * 移除settings属性，使用ProLayout的默认配置。
     * 这简化了配置并使用了推荐的默认设置。
     */
  };
};

/**
 * UmiJS 请求配置
 *
 * 这是UmiJS应用的HTTP请求配置，定义了全局的请求行为和错误处理策略。
 * 与utils/request.ts中的自定义请求实例配合使用，提供完整的请求管理。
 *
 * 配置策略：
 * 1. 使用默认配置，保持简洁性
 * 2. 错误处理委托给utils/request.ts中的拦截器
 * 3. 身份验证由请求拦截器自动处理
 * 4. 响应处理由响应拦截器统一管理
 *
 * 架构设计：
 * - app.tsx：定义UmiJS级别的请求配置
 * - utils/request.ts：实现具体的请求逻辑和拦截器
 * - services/*：使用配置好的请求实例进行API调用
 *
 * 这种分层设计确保了请求处理的一致性和可维护性。
 *
 * 更多信息见文档：https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  // 使用默认配置，所有复杂的请求处理逻辑都在utils/request.ts中实现
  // 这包括：Token注入、错误处理、响应拦截、消息提示等
};
